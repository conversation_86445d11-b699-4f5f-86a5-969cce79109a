import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/product_controller.dart';
import '../../models/product.dart';

class AddProductDialog extends StatefulWidget {
  final Product? product;

  const AddProductDialog({
    super.key,
    this.product,
  });

  @override
  State<AddProductDialog> createState() => _AddProductDialogState();
}

class _AddProductDialogState extends State<AddProductDialog> {
  final ProductController controller = Get.find<ProductController>();
  bool isEdit = false;

  @override
  void initState() {
    super.initState();
    isEdit = widget.product != null;
    
    if (isEdit) {
      // Pre-fill form with existing product data
      controller.nameController.text = widget.product!.name;
      controller.priceController.text = widget.product!.price.toString();
      controller.quantityController.text = widget.product!.quantity.toString();
      controller.descriptionController.text = widget.product!.description;
    } else {
      // Clear form for new product
      controller.clearForm();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: const BoxConstraints(maxWidth: 500),
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  isEdit ? 'Edit Product' : 'Add New Product',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // Form
            Form(
              key: controller.formKey,
              child: Column(
                children: [
                  // Product Name
                  TextFormField(
                    controller: controller.nameController,
                    decoration: const InputDecoration(
                      labelText: 'Product Name *',
                      hintText: 'Enter product name',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.shopping_bag),
                    ),
                    validator: controller.validateName,
                    textCapitalization: TextCapitalization.words,
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Price and Quantity Row
                  Row(
                    children: [
                      // Price
                      Expanded(
                        child: TextFormField(
                          controller: controller.priceController,
                          decoration: const InputDecoration(
                            labelText: 'Price *',
                            hintText: '0.00',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.currency_rupee),
                          ),
                          keyboardType: const TextInputType.numberWithOptions(decimal: true),
                          validator: controller.validatePrice,
                        ),
                      ),
                      
                      const SizedBox(width: 16),
                      
                      // Quantity
                      Expanded(
                        child: TextFormField(
                          controller: controller.quantityController,
                          decoration: const InputDecoration(
                            labelText: 'Quantity *',
                            hintText: '0',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.inventory),
                          ),
                          keyboardType: TextInputType.number,
                          validator: controller.validateQuantity,
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Description
                  TextFormField(
                    controller: controller.descriptionController,
                    decoration: const InputDecoration(
                      labelText: 'Description *',
                      hintText: 'Enter product description',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.description),
                    ),
                    maxLines: 3,
                    validator: controller.validateDescription,
                    textCapitalization: TextCapitalization.sentences,
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Action Buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                
                const SizedBox(width: 16),
                
                Obx(() => ElevatedButton(
                  onPressed: controller.isLoading.value ? null : () => _saveProduct(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).primaryColor,
                    foregroundColor: Colors.white,
                  ),
                  child: controller.isLoading.value
                      ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Text(isEdit ? 'Update' : 'Add Product'),
                )),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _saveProduct() async {
    if (isEdit) {
      // Update existing product
      final updatedProduct = widget.product!.copyWith(
        name: controller.nameController.text.trim(),
        price: double.parse(controller.priceController.text.trim()),
        quantity: int.parse(controller.quantityController.text.trim()),
        description: controller.descriptionController.text.trim(),
      );
      
      await controller.updateProduct(updatedProduct);
    } else {
      // Add new product
      await controller.addProduct();
    }
    
    if (!controller.isLoading.value && mounted) {
      Navigator.of(context).pop();
    }
  }
}
