import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../controllers/billing_controller.dart';
import '../../../models/product.dart';

class ProductSelector extends StatelessWidget {
  const ProductSelector({super.key});

  @override
  Widget build(BuildContext context) {
    final BillingController controller = Get.find<BillingController>();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Add Product to Bill',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // Product Dropdown
            Obx(() {
              if (controller.isLoading.value && controller.availableProducts.isEmpty) {
                return const Center(child: CircularProgressIndicator());
              }

              if (controller.availableProducts.isEmpty) {
                return Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.info, color: Colors.orange),
                      const SizedBox(width: 8),
                      const Text('No products available. Add products first.'),
                    ],
                  ),
                );
              }

              return Column(
                children: [
                  // Product Selection
                  DropdownButtonFormField<Product>(
                    value: controller.selectedProduct.value,
                    decoration: const InputDecoration(
                      labelText: 'Select Product',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.shopping_bag),
                    ),
                    items: controller.availableProducts.map((product) {
                      return DropdownMenuItem<Product>(
                        value: product,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              product.name,
                              style: const TextStyle(fontWeight: FontWeight.w500),
                            ),
                            Text(
                              '₹${product.price.toStringAsFixed(2)} • Stock: ${product.quantity}',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                    onChanged: (Product? product) {
                      controller.selectedProduct.value = product;
                    },
                    isExpanded: true,
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Quantity and Add Button Row
                  Row(
                    children: [
                      // Quantity Input
                      Expanded(
                        flex: 2,
                        child: TextFormField(
                          decoration: const InputDecoration(
                            labelText: 'Quantity',
                            border: OutlineInputBorder(),
                            prefixIcon: Icon(Icons.numbers),
                          ),
                          keyboardType: TextInputType.number,
                          onChanged: (value) {
                            final quantity = int.tryParse(value) ?? 1;
                            controller.selectedQuantity.value = quantity > 0 ? quantity : 1;
                          },
                          controller: TextEditingController(
                            text: controller.selectedQuantity.value.toString(),
                          ),
                        ),
                      ),
                      
                      const SizedBox(width: 16),
                      
                      // Add Button
                      Expanded(
                        flex: 3,
                        child: ElevatedButton.icon(
                          onPressed: controller.selectedProduct.value == null
                              ? null
                              : controller.addProductToBill,
                          icon: const Icon(Icons.add_shopping_cart),
                          label: const Text('Add to Bill'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Theme.of(context).primaryColor,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                          ),
                        ),
                      ),
                    ],
                  ),
                  
                  // Stock Warning
                  Obx(() {
                    final selectedProduct = controller.selectedProduct.value;
                    if (selectedProduct != null && selectedProduct.quantity <= 5) {
                      return Container(
                        margin: const EdgeInsets.only(top: 8),
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.orange.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(color: Colors.orange.withOpacity(0.3)),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.warning, size: 16, color: Colors.orange),
                            const SizedBox(width: 8),
                            Text(
                              selectedProduct.quantity == 0
                                  ? 'This product is out of stock!'
                                  : 'Low stock: Only ${selectedProduct.quantity} items remaining',
                              style: const TextStyle(
                                color: Colors.orange,
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      );
                    }
                    return const SizedBox.shrink();
                  }),
                ],
              );
            }),
          ],
        ),
      ),
    );
  }
}
