import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../controllers/billing_controller.dart';
import '../../../models/product.dart';

class ProductSelector extends StatefulWidget {
  const ProductSelector({super.key});

  @override
  State<ProductSelector> createState() => _ProductSelectorState();
}

class _ProductSelectorState extends State<ProductSelector> {
  final TextEditingController _searchController = TextEditingController();
  final RxList<Product> _filteredProducts = <Product>[].obs;
  final RxBool _showDropdown = false.obs;

  @override
  void initState() {
    super.initState();
    final controller = Get.find<BillingController>();

    // Initialize filtered products
    ever(controller.availableProducts, (products) {
      _filteredProducts.assignAll(products);
    });

    // Search functionality
    _searchController.addListener(() {
      _filterProducts();
    });
  }

  void _filterProducts() {
    final controller = Get.find<BillingController>();
    final query = _searchController.text.toLowerCase();

    if (query.isEmpty) {
      _filteredProducts.assignAll(controller.availableProducts);
    } else {
      _filteredProducts.assignAll(
        controller.availableProducts.where((product) =>
          product.name.toLowerCase().contains(query) ||
          product.description.toLowerCase().contains(query)
        ).toList()
      );
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final BillingController controller = Get.find<BillingController>();

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.add_shopping_cart,
                     color: Theme.of(context).primaryColor, size: 24),
                const SizedBox(width: 8),
                Text(
                  'Add Product to Bill',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),

            // Product Search and Selection
            Obx(() {
              if (controller.isLoading.value && controller.availableProducts.isEmpty) {
                return const Center(
                  child: Padding(
                    padding: EdgeInsets.all(20),
                    child: CircularProgressIndicator(),
                  ),
                );
              }

              if (controller.availableProducts.isEmpty) {
                return Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    color: Colors.orange.withOpacity(0.1),
                    border: Border.all(color: Colors.orange.withOpacity(0.3)),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.info_outline, color: Colors.orange),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          'No products available. Please add products first.',
                          style: TextStyle(color: Colors.orange[800]),
                        ),
                      ),
                    ],
                  ),
                );
              }

              return Column(
                children: [
                  // Search Field
                  TextFormField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      labelText: 'Search Products',
                      hintText: 'Type to search products...',
                      prefixIcon: const Icon(Icons.search),
                      suffixIcon: _searchController.text.isNotEmpty
                          ? IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: () {
                                _searchController.clear();
                                _showDropdown.value = false;
                              },
                            )
                          : null,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      filled: true,
                      fillColor: Colors.grey[50],
                    ),
                    onTap: () => _showDropdown.value = true,
                    onChanged: (value) {
                      _showDropdown.value = value.isNotEmpty;
                    },
                  ),

                  const SizedBox(height: 12),

                  // Dropdown List
                  Obx(() => _showDropdown.value && _filteredProducts.isNotEmpty
                      ? Container(
                          constraints: const BoxConstraints(maxHeight: 200),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey[300]!),
                            borderRadius: BorderRadius.circular(12),
                            color: Colors.white,
                          ),
                          child: ListView.builder(
                            shrinkWrap: true,
                            itemCount: _filteredProducts.length,
                            itemBuilder: (context, index) {
                              final product = _filteredProducts[index];
                              return ListTile(
                                leading: CircleAvatar(
                                  backgroundColor: Theme.of(context).primaryColor.withOpacity(0.1),
                                  child: Text(
                                    product.name[0].toUpperCase(),
                                    style: TextStyle(
                                      color: Theme.of(context).primaryColor,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                                title: Text(
                                  product.name,
                                  style: const TextStyle(fontWeight: FontWeight.w600),
                                ),
                                subtitle: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      product.description,
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                    const SizedBox(height: 2),
                                    Row(
                                      children: [
                                        Text(
                                          '₹${product.price.toStringAsFixed(2)}',
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            color: Colors.green,
                                          ),
                                        ),
                                        const SizedBox(width: 8),
                                        Text(
                                          'Stock: ${product.quantity}',
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: product.quantity <= 5
                                                ? Colors.orange
                                                : Colors.grey[600],
                                            fontWeight: product.quantity <= 5
                                                ? FontWeight.w600
                                                : FontWeight.normal,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                                trailing: product.quantity == 0
                                    ? Container(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 8, vertical: 4),
                                        decoration: BoxDecoration(
                                          color: Colors.red.withOpacity(0.1),
                                          borderRadius: BorderRadius.circular(12),
                                        ),
                                        child: const Text(
                                          'Out of Stock',
                                          style: TextStyle(
                                            color: Colors.red,
                                            fontSize: 10,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      )
                                    : null,
                                onTap: () {
                                  controller.selectedProduct.value = product;
                                  _searchController.text = product.name;
                                  _showDropdown.value = false;
                                },
                              );
                            },
                          ),
                        )
                      : const SizedBox.shrink()),

                  const SizedBox(height: 20),

                  // Selected Product Display
                  Obx(() => controller.selectedProduct.value != null
                      ? Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Theme.of(context).primaryColor.withValues(alpha: 0.05),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: Theme.of(context).primaryColor.withValues(alpha: 0.2),
                            ),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Selected Product',
                                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: Theme.of(context).primaryColor,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                controller.selectedProduct.value!.name,
                                style: const TextStyle(
                                  fontWeight: FontWeight.w600,
                                  fontSize: 16,
                                ),
                              ),
                              Text(
                                '₹${controller.selectedProduct.value!.price.toStringAsFixed(2)} per item',
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        )
                      : const SizedBox.shrink()),

                  const SizedBox(height: 16),

                  // Quantity and Add Button Row
                  Row(
                    children: [
                      // Quantity Input
                      Expanded(
                        flex: 2,
                        child: TextFormField(
                          decoration: InputDecoration(
                            labelText: 'Quantity',
                            hintText: '1',
                            prefixIcon: const Icon(Icons.numbers),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            filled: true,
                            fillColor: Colors.grey[50],
                          ),
                          keyboardType: TextInputType.number,
                          onChanged: (value) {
                            final quantity = int.tryParse(value) ?? 1;
                            controller.selectedQuantity.value = quantity > 0 ? quantity : 1;
                          },
                          controller: TextEditingController(
                            text: controller.selectedQuantity.value.toString(),
                          ),
                        ),
                      ),

                      const SizedBox(width: 16),

                      // Add Button
                      Expanded(
                        flex: 3,
                        child: Obx(() => ElevatedButton.icon(
                          onPressed: controller.selectedProduct.value == null
                              ? null
                              : controller.addProductToBill,
                          icon: const Icon(Icons.add_shopping_cart),
                          label: const Text('Add to Bill'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Theme.of(context).primaryColor,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            elevation: 2,
                          ),
                        )),
                      ),
                    ],
                  ),

                  // Stock Warning
                  Obx(() {
                    final selectedProduct = controller.selectedProduct.value;
                    if (selectedProduct != null && selectedProduct.quantity <= 5) {
                      return Container(
                        margin: const EdgeInsets.only(top: 12),
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.orange.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.warning_amber_rounded,
                                      size: 20, color: Colors.orange),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                selectedProduct.quantity == 0
                                    ? 'This product is out of stock!'
                                    : 'Low stock: Only ${selectedProduct.quantity} items remaining',
                                style: TextStyle(
                                  color: Colors.orange[800],
                                  fontSize: 13,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    }
                    return const SizedBox.shrink();
                  }),
                ],
              );
            }),
          ],
        ),
      ),
    );
  }
}
