import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../../controllers/history_controller.dart';

class DateFilterBar extends StatelessWidget {
  const DateFilterBar({super.key});

  @override
  Widget build(BuildContext context) {
    final HistoryController controller = Get.find<HistoryController>();
    final dateFormat = DateFormat('dd/MM/yyyy');

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(
          bottom: BorderSide(color: Colors.grey[200]!),
        ),
      ),
      child: Column(
        children: [
          // Quick Filter Buttons
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _FilterChip(
                  label: 'Today',
                  onPressed: controller.getTodaysBills,
                ),
                const SizedBox(width: 8),
                _FilterChip(
                  label: 'This Week',
                  onPressed: controller.getThisWeeksBills,
                ),
                const SizedBox(width: 8),
                _FilterChip(
                  label: 'This Month',
                  onPressed: controller.getThisMonthsBills,
                ),
                const SizedBox(width: 8),
                _FilterChip(
                  label: 'All Time',
                  onPressed: controller.clearDateRange,
                ),
                const SizedBox(width: 16),
                OutlinedButton.icon(
                  onPressed: () => _showDateRangePicker(context, controller),
                  icon: const Icon(Icons.date_range, size: 16),
                  label: const Text('Custom Range'),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                ),
              ],
            ),
          ),
          
          // Current Filter Display
          Obx(() {
            if (controller.startDate.value != null || controller.endDate.value != null) {
              return Container(
                margin: const EdgeInsets.only(top: 12),
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.filter_alt,
                      size: 16,
                      color: Theme.of(context).primaryColor,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      controller.startDate.value != null && controller.endDate.value != null
                          ? '${dateFormat.format(controller.startDate.value!)} - ${dateFormat.format(controller.endDate.value!)}'
                          : controller.startDate.value != null
                              ? 'From ${dateFormat.format(controller.startDate.value!)}'
                              : 'Until ${dateFormat.format(controller.endDate.value!)}',
                      style: TextStyle(
                        color: Theme.of(context).primaryColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(width: 8),
                    GestureDetector(
                      onTap: controller.clearDateRange,
                      child: Icon(
                        Icons.close,
                        size: 16,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ],
                ),
              );
            }
            return const SizedBox.shrink();
          }),
        ],
      ),
    );
  }

  Future<void> _showDateRangePicker(BuildContext context, HistoryController controller) async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: controller.startDate.value != null && controller.endDate.value != null
          ? DateTimeRange(
              start: controller.startDate.value!,
              end: controller.endDate.value!,
            )
          : null,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: Theme.of(context).primaryColor,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      controller.setDateRange(picked.start, picked.end);
    }
  }
}

class _FilterChip extends StatelessWidget {
  final String label;
  final VoidCallback onPressed;

  const _FilterChip({
    required this.label,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return ActionChip(
      label: Text(
        label,
        style: const TextStyle(fontSize: 12),
      ),
      onPressed: onPressed,
      backgroundColor: Colors.white,
      side: BorderSide(color: Colors.grey[300]!),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
    );
  }
}
