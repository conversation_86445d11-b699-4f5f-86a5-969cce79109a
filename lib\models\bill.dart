import 'bill_item.dart';

class Bill {
  final String id;
  final List<BillItem> items;
  final double subtotal;
  final double tax;
  final double total;
  final DateTime createdAt;
  final String customerName;
  final String customerPhone;

  Bill({
    required this.id,
    required this.items,
    this.tax = 0.0,
    required this.createdAt,
    this.customerName = '',
    this.customerPhone = '',
  }) : subtotal = items.fold(0.0, (sum, item) => sum + item.total),
       total = items.fold(0.0, (sum, item) => sum + item.total) + tax;

  // Convert Bill to Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'items': items.map((item) => item.toMap()).toList(),
      'subtotal': subtotal,
      'tax': tax,
      'total': total,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'customerName': customerName,
      'customerPhone': customerPhone,
    };
  }

  // Create Bill from Firestore Map
  factory Bill.fromMap(Map<String, dynamic> map) {
    return Bill(
      id: map['id'] ?? '',
      items: (map['items'] as List<dynamic>?)
          ?.map((item) => BillItem.fromMap(item as Map<String, dynamic>))
          .toList() ?? [],
      tax: (map['tax'] ?? 0).toDouble(),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['createdAt'] ?? 0),
      customerName: map['customerName'] ?? '',
      customerPhone: map['customerPhone'] ?? '',
    );
  }

  // Create a copy of Bill with updated fields
  Bill copyWith({
    String? id,
    List<BillItem>? items,
    double? tax,
    DateTime? createdAt,
    String? customerName,
    String? customerPhone,
  }) {
    return Bill(
      id: id ?? this.id,
      items: items ?? this.items,
      tax: tax ?? this.tax,
      createdAt: createdAt ?? this.createdAt,
      customerName: customerName ?? this.customerName,
      customerPhone: customerPhone ?? this.customerPhone,
    );
  }

  @override
  String toString() {
    return 'Bill(id: $id, items: ${items.length}, total: $total, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Bill && other.id == id;
  }

  @override
  int get hashCode {
    return id.hashCode;
  }
}
