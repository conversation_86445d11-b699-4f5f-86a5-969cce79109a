class BillItem {
  final String productId;
  final String productName;
  final double price;
  final int quantity;
  final double total;

  BillItem({
    required this.productId,
    required this.productName,
    required this.price,
    required this.quantity,
  }) : total = price * quantity;

  // Convert BillItem to Map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'productId': productId,
      'productName': productName,
      'price': price,
      'quantity': quantity,
      'total': total,
    };
  }

  // Create BillItem from Firestore Map
  factory BillItem.fromMap(Map<String, dynamic> map) {
    return BillItem(
      productId: map['productId'] ?? '',
      productName: map['productName'] ?? '',
      price: (map['price'] ?? 0).toDouble(),
      quantity: map['quantity'] ?? 0,
    );
  }

  // Create a copy of BillItem with updated fields
  BillItem copyWith({
    String? productId,
    String? productName,
    double? price,
    int? quantity,
  }) {
    return BillItem(
      productId: productId ?? this.productId,
      productName: productName ?? this.productName,
      price: price ?? this.price,
      quantity: quantity ?? this.quantity,
    );
  }

  @override
  String toString() {
    return 'BillItem(productId: $productId, productName: $productName, price: $price, quantity: $quantity, total: $total)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is BillItem && 
           other.productId == productId && 
           other.quantity == quantity;
  }

  @override
  int get hashCode {
    return productId.hashCode ^ quantity.hashCode;
  }
}
