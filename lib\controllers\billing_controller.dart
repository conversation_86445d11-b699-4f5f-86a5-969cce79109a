import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import '../models/product.dart';
import '../models/bill.dart';
import '../models/bill_item.dart';
import '../services/product_service.dart';
import '../services/bill_service.dart';

class BillingController extends GetxController {
  // Observable lists and variables
  final RxList<Product> availableProducts = <Product>[].obs;
  final RxList<BillItem> billItems = <BillItem>[].obs;
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;
  final RxDouble subtotal = 0.0.obs;
  final RxDouble tax = 0.0.obs;
  final RxDouble total = 0.0.obs;

  // Selected product and quantity
  final Rx<Product?> selectedProduct = Rx<Product?>(null);
  final RxInt selectedQuantity = 1.obs;

  // Customer information
  final customerNameController = TextEditingController();
  final customerPhoneController = TextEditingController();

  @override
  void onInit() {
    super.onInit();
    loadProducts();
    
    // Listen to bill items changes to update totals
    ever(billItems, (_) => calculateTotals());
  }

  @override
  void onClose() {
    customerNameController.dispose();
    customerPhoneController.dispose();
    super.onClose();
  }

  // Load all available products
  Future<void> loadProducts() async {
    try {
      isLoading.value = true;
      errorMessage.value = '';
      
      final productList = await ProductService.getAllProducts();
      availableProducts.assignAll(productList);
    } catch (e) {
      errorMessage.value = e.toString();
      Get.snackbar(
        'Error',
        'Failed to load products: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  // Add product to bill
  void addProductToBill() {
    if (selectedProduct.value == null) {
      Get.snackbar(
        'Error',
        'Please select a product',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    if (selectedQuantity.value <= 0) {
      Get.snackbar(
        'Error',
        'Please enter a valid quantity',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    final product = selectedProduct.value!;
    
    // Check if product already exists in bill
    final existingItemIndex = billItems.indexWhere(
      (item) => item.productId == product.id,
    );

    if (existingItemIndex != -1) {
      // Update existing item quantity
      final existingItem = billItems[existingItemIndex];
      final newQuantity = existingItem.quantity + selectedQuantity.value;
      
      billItems[existingItemIndex] = existingItem.copyWith(quantity: newQuantity);
    } else {
      // Add new item
      final billItem = BillItem(
        productId: product.id,
        productName: product.name,
        price: product.price,
        quantity: selectedQuantity.value,
      );
      
      billItems.add(billItem);
    }

    // Reset selection
    selectedProduct.value = null;
    selectedQuantity.value = 1;
    
    Get.snackbar(
      'Success',
      'Product added to bill',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: Colors.green,
      colorText: Colors.white,
    );
  }

  // Remove product from bill
  void removeProductFromBill(int index) {
    if (index >= 0 && index < billItems.length) {
      billItems.removeAt(index);
    }
  }

  // Update item quantity in bill
  void updateItemQuantity(int index, int newQuantity) {
    if (index >= 0 && index < billItems.length && newQuantity > 0) {
      final item = billItems[index];
      billItems[index] = item.copyWith(quantity: newQuantity);
    }
  }

  // Update item price in bill
  void updateItemPrice(int index, double newPrice) {
    if (index >= 0 && index < billItems.length && newPrice > 0) {
      final item = billItems[index];
      billItems[index] = item.copyWith(price: newPrice);
    }
  }

  // Calculate totals
  void calculateTotals() {
    subtotal.value = billItems.fold(0.0, (sum, item) => sum + item.total);
    total.value = subtotal.value + tax.value;
  }

  // Set tax amount
  void setTax(double taxAmount) {
    tax.value = taxAmount;
    calculateTotals();
  }

  // Save bill to Firestore
  Future<void> saveBill() async {
    if (billItems.isEmpty) {
      Get.snackbar(
        'Error',
        'Please add at least one product to the bill',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    try {
      isLoading.value = true;
      errorMessage.value = '';

      final bill = Bill(
        id: const Uuid().v4(),
        items: billItems.toList(),
        tax: tax.value,
        createdAt: DateTime.now(),
        customerName: customerNameController.text.trim(),
        customerPhone: customerPhoneController.text.trim(),
      );

      await BillService.addBill(bill);
      
      // Clear the current bill
      clearBill();
      
      Get.snackbar(
        'Success',
        'Bill saved successfully!',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      errorMessage.value = e.toString();
      Get.snackbar(
        'Error',
        'Failed to save bill: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  // Clear current bill
  void clearBill() {
    billItems.clear();
    selectedProduct.value = null;
    selectedQuantity.value = 1;
    tax.value = 0.0;
    customerNameController.clear();
    customerPhoneController.clear();
    calculateTotals();
  }

  // Get current bill as Bill object
  Bill getCurrentBill() {
    return Bill(
      id: const Uuid().v4(),
      items: billItems.toList(),
      tax: tax.value,
      createdAt: DateTime.now(),
      customerName: customerNameController.text.trim(),
      customerPhone: customerPhoneController.text.trim(),
    );
  }
}
