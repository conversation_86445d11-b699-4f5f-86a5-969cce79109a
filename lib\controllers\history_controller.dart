import 'package:get/get.dart';
import 'package:flutter/material.dart';
import '../models/bill.dart';
import '../services/bill_service.dart';

class HistoryController extends GetxController {
  // Observable lists and variables
  final RxList<Bill> bills = <Bill>[].obs;
  final RxBool isLoading = false.obs;
  final RxString errorMessage = ''.obs;
  final RxDouble totalSales = 0.0.obs;

  // Date range for filtering
  final Rx<DateTime?> startDate = Rx<DateTime?>(null);
  final Rx<DateTime?> endDate = Rx<DateTime?>(null);

  @override
  void onInit() {
    super.onInit();
    loadBills();
  }

  // Load all bills
  Future<void> loadBills() async {
    try {
      isLoading.value = true;
      errorMessage.value = '';
      
      List<Bill> billList;
      
      if (startDate.value != null && endDate.value != null) {
        billList = await BillService.getBillsByDateRange(
          startDate.value!,
          endDate.value!,
        );
      } else {
        billList = await BillService.getAllBills();
      }
      
      bills.assignAll(billList);
      calculateTotalSales();
    } catch (e) {
      errorMessage.value = e.toString();
      Get.snackbar(
        'Error',
        'Failed to load bills: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  // Calculate total sales from loaded bills
  void calculateTotalSales() {
    totalSales.value = bills.fold(0.0, (sum, bill) => sum + bill.total);
  }

  // Set date range filter
  void setDateRange(DateTime? start, DateTime? end) {
    startDate.value = start;
    endDate.value = end;
    loadBills();
  }

  // Clear date range filter
  void clearDateRange() {
    startDate.value = null;
    endDate.value = null;
    loadBills();
  }

  // Delete a bill
  Future<void> deleteBill(String billId) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      await BillService.deleteBill(billId);
      await loadBills();
      
      Get.snackbar(
        'Success',
        'Bill deleted successfully!',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      errorMessage.value = e.toString();
      Get.snackbar(
        'Error',
        'Failed to delete bill: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoading.value = false;
    }
  }

  // Get bill by ID
  Future<Bill?> getBillById(String billId) async {
    try {
      return await BillService.getBillById(billId);
    } catch (e) {
      Get.snackbar(
        'Error',
        'Failed to get bill: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return null;
    }
  }

  // Refresh bills
  Future<void> refreshBills() async {
    await loadBills();
  }

  // Get bills for today
  void getTodaysBills() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(const Duration(days: 1));
    
    setDateRange(today, tomorrow);
  }

  // Get bills for this week
  void getThisWeeksBills() {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final endOfWeek = startOfWeek.add(const Duration(days: 6));
    
    setDateRange(
      DateTime(startOfWeek.year, startOfWeek.month, startOfWeek.day),
      DateTime(endOfWeek.year, endOfWeek.month, endOfWeek.day, 23, 59, 59),
    );
  }

  // Get bills for this month
  void getThisMonthsBills() {
    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);
    final endOfMonth = DateTime(now.year, now.month + 1, 0, 23, 59, 59);
    
    setDateRange(startOfMonth, endOfMonth);
  }

  // Format date for display
  String formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  // Format currency for display
  String formatCurrency(double amount) {
    return '₹${amount.toStringAsFixed(2)}';
  }
}
