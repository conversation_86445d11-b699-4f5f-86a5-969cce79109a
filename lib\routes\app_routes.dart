import 'package:go_router/go_router.dart';
import 'package:flutter/material.dart';
import '../views/main_screen.dart';
import '../views/products/products_screen.dart';
import '../views/billing/billing_screen.dart';
import '../views/history/history_screen.dart';

class AppRoutes {
  static const String home = '/';
  static const String products = '/products';
  static const String billing = '/billing';
  static const String history = '/history';

  static final GoRouter router = GoRouter(
    initialLocation: home,
    routes: [
      ShellRoute(
        builder: (context, state, child) {
          return MainScreen(child: child);
        },
        routes: [
          GoRoute(
            path: home,
            redirect: (context, state) => products,
          ),
          GoRoute(
            path: products,
            pageBuilder: (context, state) => const NoTransitionPage(
              child: ProductsScreen(),
            ),
          ),
          GoRoute(
            path: billing,
            pageBuilder: (context, state) => const NoTransitionPage(
              child: BillingScreen(),
            ),
          ),
          GoRoute(
            path: history,
            pageBuilder: (context, state) => const NoTransitionPage(
              child: HistoryScreen(),
            ),
          ),
        ],
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red,
            ),
            const SizedBox(height: 16),
            Text(
              'Page not found',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'The page you are looking for does not exist.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go(products),
              child: const Text('Go to Products'),
            ),
          ],
        ),
      ),
    ),
  );
}
