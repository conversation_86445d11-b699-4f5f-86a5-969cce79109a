import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/bill.dart';

class BillService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _collection = 'bills';

  // Add a new bill
  static Future<String> addBill(Bill bill) async {
    try {
      DocumentReference docRef = await _firestore
          .collection(_collection)
          .add(bill.toMap());
      
      // Update the bill with the generated ID
      await docRef.update({'id': docRef.id});
      
      return docRef.id;
    } catch (e) {
      throw Exception('Failed to add bill: $e');
    }
  }

  // Get all bills
  static Future<List<Bill>> getAllBills() async {
    try {
      QuerySnapshot querySnapshot = await _firestore
          .collection(_collection)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => Bill.fromMap(doc.data() as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw Exception('Failed to get bills: $e');
    }
  }

  // Get bills stream for real-time updates
  static Stream<List<Bill>> getBillsStream() {
    return _firestore
        .collection(_collection)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => Bill.fromMap(doc.data() as Map<String, dynamic>))
            .toList());
  }

  // Get a single bill by ID
  static Future<Bill?> getBillById(String id) async {
    try {
      DocumentSnapshot doc = await _firestore
          .collection(_collection)
          .doc(id)
          .get();

      if (doc.exists) {
        return Bill.fromMap(doc.data() as Map<String, dynamic>);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get bill: $e');
    }
  }

  // Update a bill
  static Future<void> updateBill(Bill bill) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(bill.id)
          .update(bill.toMap());
    } catch (e) {
      throw Exception('Failed to update bill: $e');
    }
  }

  // Delete a bill
  static Future<void> deleteBill(String id) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(id)
          .delete();
    } catch (e) {
      throw Exception('Failed to delete bill: $e');
    }
  }

  // Get bills by date range
  static Future<List<Bill>> getBillsByDateRange(DateTime startDate, DateTime endDate) async {
    try {
      QuerySnapshot querySnapshot = await _firestore
          .collection(_collection)
          .where('createdAt', isGreaterThanOrEqualTo: startDate.millisecondsSinceEpoch)
          .where('createdAt', isLessThanOrEqualTo: endDate.millisecondsSinceEpoch)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => Bill.fromMap(doc.data() as Map<String, dynamic>))
          .toList();
    } catch (e) {
      throw Exception('Failed to get bills by date range: $e');
    }
  }

  // Get total sales for a date range
  static Future<double> getTotalSales(DateTime startDate, DateTime endDate) async {
    try {
      QuerySnapshot querySnapshot = await _firestore
          .collection(_collection)
          .where('createdAt', isGreaterThanOrEqualTo: startDate.millisecondsSinceEpoch)
          .where('createdAt', isLessThanOrEqualTo: endDate.millisecondsSinceEpoch)
          .get();

      double total = 0.0;
      for (var doc in querySnapshot.docs) {
        final bill = Bill.fromMap(doc.data() as Map<String, dynamic>);
        total += bill.total;
      }
      
      return total;
    } catch (e) {
      throw Exception('Failed to get total sales: $e');
    }
  }
}
